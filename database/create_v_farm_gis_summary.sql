-- =====================================================
-- Farm GIS Summary View Creation Script
-- =====================================================

-- Drop the view if it exists
DROP VIEW IF EXISTS `v_farm_gis_summary`;

-- Create the v_farm_gis_summary view
CREATE OR REPLACE VIEW `v_farm_gis_summary` AS
SELECT 
  f.id AS farm_id,
  f.name AS farm_name,
  f.latitude AS latitude,
  f.longitude AS longitude,
  f.elevation AS elevation,
  COUNT(DISTINCT fl.id) AS total_layers,
  COUNT(DISTINCT ft.id) AS total_features,
  COUNT(DISTINCT ws.id) AS weather_stations,
  COUNT(DISTINCT sp.id) AS soil_samples
FROM farms f
LEFT JOIN farm_gis_layers fl ON f.id = fl.farm_id
LEFT JOIN farm_gis_features ft ON f.id = ft.farm_id AND ft.is_active = 1
LEFT JOIN farm_weather_stations ws ON f.id = ws.farm_id AND ws.is_active = 1
LEFT JOIN fields fd ON f.id = fd.farm_id
LEFT JOIN soil_sampling_points sp ON fd.id = sp.field_id
WHERE f.status = 'active'
GROUP BY f.id, f.name, f.latitude, f.longitude, f.elevation;

-- Usage example:
-- SELECT * FROM v_farm_gis_summary;
